from sqlalchemy import Integer, String, func, Index, UniqueConstraint, Text
from app import db


class HistoricalChemical(db.Model):
    """
    SQLAlchemy model for historical chemical data.
    Represents chemicals with their HS codes and product families.
    """
    __tablename__ = 'historical_chemicals'

    # Primary key
    id = db.Column(Integer, primary_key=True, autoincrement=True)

    # Chemical information (matching CSV columns)
    # Increased product_name to handle longer names with special characters
    product_name = db.Column(String(800), nullable=False, index=True)

    # Increased hs_code to handle variations like '3811.19.00.00' (13 chars) + buffer
    hs_code = db.Column(String(30), nullable=False, index=True)

    # Changed to Text for unlimited length product families
    product_family = db.Column(Text, nullable=False)

    # Additional fields for future extensibility
    cas_number = db.Column(String(50), nullable=True, index=True)  # Chemical Abstract Service number
    chemical_formula = db.Column(String(200), nullable=True)  # Chemical formula like (CH₃COOH)
    alternative_names = db.Column(Text, nullable=True)  # JSON array of alternative names
    regulatory_notes = db.Column(Text, nullable=True)  # Regulatory information
    source = db.Column(String(100), nullable=True, default='historical_csv')  # Data source tracking

    # Metadata fields
    created_at = db.Column(db.DateTime, server_default=func.now())
    updated_at = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())

    # Database constraints and indexes for performance and data integrity
    __table_args__ = (
        # Unique constraint to prevent duplicate product_name + hs_code combinations
        UniqueConstraint('product_name', 'hs_code', name='uq_product_hs_code'),

        # Performance indexes
        Index('idx_product_name', 'product_name'),
        Index('idx_hs_code', 'hs_code'),
        Index('idx_cas_number', 'cas_number'),
        Index('idx_source', 'source'),

        # Full-text search index for product names (PostgreSQL specific)
        Index('idx_product_name_gin', 'product_name', postgresql_using='gin', postgresql_ops={'product_name': 'gin_trgm_ops'}),
    )

    def __init__(self, product_name, hs_code, product_family, cas_number=None,
                 chemical_formula=None, alternative_names=None, regulatory_notes=None,
                 source='historical_csv'):
        """
        Initialize a new HistoricalChemical instance.

        Args:
            product_name (str): Name of the chemical product
            hs_code (str): Harmonized System code
            product_family (str): Product family/application description
            cas_number (str, optional): Chemical Abstract Service number
            chemical_formula (str, optional): Chemical formula
            alternative_names (str, optional): JSON string of alternative names
            regulatory_notes (str, optional): Regulatory information
            source (str): Data source (default: 'historical_csv')
        """
        # Validate and clean input data
        self.product_name = self._clean_text(product_name)
        self.hs_code = self._clean_hs_code(hs_code)
        self.product_family = self._clean_text(product_family)
        self.cas_number = self._clean_text(cas_number) if cas_number else None
        self.chemical_formula = self._clean_text(chemical_formula) if chemical_formula else None
        self.alternative_names = alternative_names
        self.regulatory_notes = regulatory_notes
        self.source = source

    def _clean_text(self, text):
        """Clean and validate text input."""
        if not text:
            return text
        # Strip whitespace and handle special characters
        cleaned = str(text).strip()
        # Handle empty strings
        return cleaned if cleaned else None

    def _clean_hs_code(self, hs_code):
        """Clean and validate HS code format."""
        if not hs_code:
            raise ValueError("HS code cannot be empty")

        # Remove any whitespace
        cleaned = str(hs_code).strip()

        # Basic validation - HS codes should contain only digits and dots
        import re
        if not re.match(r'^[\d.]+$', cleaned):
            raise ValueError(f"Invalid HS code format: {cleaned}")

        return cleaned

    def to_dict(self):
        """
        Convert the model instance to a dictionary.

        Returns:
            dict: Dictionary representation of the chemical
        """
        return {
            'id': self.id,
            'product_name': self.product_name,
            'hs_code': self.hs_code,
            'product_family': self.product_family,
            'cas_number': self.cas_number,
            'chemical_formula': self.chemical_formula,
            'alternative_names': self.alternative_names,
            'regulatory_notes': self.regulatory_notes,
            'source': self.source,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def to_csv_format(self):
        """
        Convert to the original CSV format for backward compatibility.

        Returns:
            dict: Dictionary matching original CSV columns
        """
        return {
            'product_name': self.product_name,
            'hs_code': self.hs_code,
            'product_family': self.product_family
        }

    @classmethod
    def create_from_csv_row(cls, row_dict):
        """
        Create a HistoricalChemical instance from a CSV row dictionary.

        Args:
            row_dict (dict): Dictionary with keys matching CSV columns

        Returns:
            HistoricalChemical: New instance
        """
        return cls(
            product_name=row_dict.get('product_name'),
            hs_code=row_dict.get('hs_code'),
            product_family=row_dict.get('product_family'),
            source='historical_csv'
        )

    @classmethod
    def find_by_product_name(cls, product_name):
        """
        Find chemical by product name.

        Args:
            product_name (str): Name to search for

        Returns:
            HistoricalChemical or None: Found chemical or None
        """
        query = cls.query.filter(cls.product_name.ilike(f'%{product_name}%'))
        return query.first()

    @classmethod
    def find_by_hs_code(cls, hs_code):
        """
        Find chemicals by HS code.

        Args:
            hs_code (str): HS code to search for

        Returns:
            list: List of matching chemicals
        """
        query = cls.query.filter(cls.hs_code == hs_code)
        return query.all()

    @classmethod
    def get_all(cls):
        """
        Get all chemicals.

        Returns:
            list: List of all chemicals
        """
        return cls.query.order_by(cls.product_name).all()

    @classmethod
    def search_chemicals(cls, search_term):
        """
        Search chemicals by product name or product family.

        Args:
            search_term (str): Term to search for

        Returns:
            list: List of matching chemicals
        """
        if not search_term:
            return []

        search_pattern = f'%{search_term}%'
        query = cls.query.filter(
            db.or_(
                cls.product_name.ilike(search_pattern),
                cls.product_family.ilike(search_pattern),
                cls.alternative_names.ilike(search_pattern) if cls.alternative_names else False
            )
        )
        return query.order_by(cls.product_name).all()

    @classmethod
    def find_exact_match(cls, product_name, hs_code):
        """
        Find exact match by product name and HS code.

        Args:
            product_name (str): Exact product name
            hs_code (str): Exact HS code

        Returns:
            HistoricalChemical or None: Found chemical or None
        """
        query = cls.query.filter(
            cls.product_name == product_name,
            cls.hs_code == hs_code
        )
        return query.first()

    @classmethod
    def get_all_hs_codes(cls):
        """
        Get all unique HS codes.

        Returns:
            list: List of unique HS codes
        """
        query = cls.query.with_entities(cls.hs_code).distinct()
        return [row[0] for row in query.all()]

    @classmethod
    def bulk_create_from_csv(cls, csv_data):
        """
        Bulk create chemicals from CSV data with error handling.

        Args:
            csv_data (list): List of dictionaries from CSV

        Returns:
            dict: Summary of creation results
        """
        results = {
            'created': 0,
            'skipped': 0,
            'errors': []
        }

        for row in csv_data:
            try:
                # Check if already exists
                existing = cls.find_exact_match(
                    row.get('product_name'),
                    row.get('hs_code'),
                    active_only=False
                )

                if existing:
                    results['skipped'] += 1
                    continue

                # Create new chemical
                chemical = cls.create_from_csv_row(row)
                db.session.add(chemical)
                results['created'] += 1

            except Exception as e:
                results['errors'].append({
                    'row': row,
                    'error': str(e)
                })

        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            results['errors'].append({
                'error': f"Database commit failed: {str(e)}"
            })

        return results

    def __repr__(self):
        """String representation of the chemical."""
        return f'<HistoricalChemical {self.product_name} ({self.hs_code})>'
