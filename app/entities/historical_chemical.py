from sqlalchemy import Integer, String, func, Index
from app import db


class HistoricalChemical(db.Model):
    """
    SQLAlchemy model for historical chemical data.
    Represents chemicals with their HS codes and product families.
    """
    __tablename__ = 'historical_chemicals'
    
    # Primary key
    id = db.Column(Integer, primary_key=True, autoincrement=True)
    
    # Chemical information (matching CSV columns)
    product_name = db.Column(String(500), nullable=False, index=True)
    hs_code = db.Column(String(20), nullable=False, index=True)
    product_family = db.Column(String(1000), nullable=False)
    
    # Metadata fields
    created_at = db.Column(db.DateTime, server_default=func.now())
    updated_at = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Database indexes for performance
    __table_args__ = (
        Index('idx_product_name_hs_code', 'product_name', 'hs_code'),
        Index('idx_hs_code_active', 'hs_code', 'is_active'),
    )

    def __init__(self, product_name, hs_code, product_family, is_active=True):
        """
        Initialize a new HistoricalChemical instance.
        
        Args:
            product_name (str): Name of the chemical product
            hs_code (str): Harmonized System code
            product_family (str): Product family/application description
            is_active (bool): Whether the record is active (default: True)
        """
        self.product_name = product_name
        self.hs_code = hs_code
        self.product_family = product_family
        self.is_active = is_active

    # def to_dict(self):
    #     """
    #     Convert the model instance to a dictionary.
        
    #     Returns:
    #         dict: Dictionary representation of the chemical
    #     """
    #     return {
    #         'id': self.id,
    #         'product_name': self.product_name,
    #         'hs_code': self.hs_code,
    #         'product_family': self.product_family,
    #         'is_active': self.is_active,
    #         'created_at': self.created_at.isoformat() if self.created_at else None,
    #         'updated_at': self.updated_at.isoformat() if self.updated_at else None
    #     }

    # def to_csv_format(self):
    #     """
    #     Convert to the original CSV format for backward compatibility.
        
    #     Returns:
    #         dict: Dictionary matching original CSV columns
    #     """
    #     return {
    #         'product_name': self.product_name,
    #         'hs_code': self.hs_code,
    #         'product_family': self.product_family
    #     }

    @classmethod
    def find_by_product_name(cls, product_name, active_only=True):
        """
        Find chemical by product name.
        
        Args:
            product_name (str): Name to search for
            active_only (bool): Only return active records
            
        Returns:
            HistoricalChemical or None: Found chemical or None
        """
        query = cls.query.filter(cls.product_name.ilike(f'%{product_name}%'))
        if active_only:
            query = query.filter(cls.is_active == True)
        return query.first()

    @classmethod
    def find_by_hs_code(cls, hs_code, active_only=True):
        """
        Find chemicals by HS code.
        
        Args:
            hs_code (str): HS code to search for
            active_only (bool): Only return active records
            
        Returns:
            list: List of matching chemicals
        """
        query = cls.query.filter(cls.hs_code == hs_code)
        if active_only:
            query = query.filter(cls.is_active == True)
        return query.all()

    @classmethod
    def get_all_active(cls):
        """
        Get all active chemicals.
        
        Returns:
            list: List of all active chemicals
        """
        return cls.query.filter(cls.is_active == True).order_by(cls.product_name).all()

    @classmethod
    def search_chemicals(cls, search_term, active_only=True):
        """
        Search chemicals by product name or product family.
        
        Args:
            search_term (str): Term to search for
            active_only (bool): Only return active records
            
        Returns:
            list: List of matching chemicals
        """
        search_pattern = f'%{search_term}%'
        query = cls.query.filter(
            db.or_(
                cls.product_name.ilike(search_pattern),
                cls.product_family.ilike(search_pattern)
            )
        )
        if active_only:
            query = query.filter(cls.is_active == True)
        return query.order_by(cls.product_name).all()

    def __repr__(self):
        """String representation of the chemical."""
        return f'<HistoricalChemical {self.product_name} ({self.hs_code})>'
